/**
 * Test script for v3 API endpoints
 * Run with: node scripts/test-v3-api.js
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3000';
let authToken = '';

// Test data
const testUser = {
  email: '<EMAIL>',
  password: 'password123',
  fullName: 'Test User'
};

const testContact = {
  email: '<EMAIL>',
  fullName: 'Test Contact',
  address: '123 Test Street',
  phone: '1234567890',
  type: 'Friend'
};

// Helper function to make authenticated requests
const authRequest = (method, url, data = null) => {
  return axios({
    method,
    url: `${BASE_URL}${url}`,
    data,
    headers: authToken ? { Authorization: `Bearer ${authToken}` } : {}
  });
};

// Test functions
async function testRegistration() {
  console.log('🧪 Testing user registration...');
  try {
    const response = await authRequest('POST', '/api/v3/auth/register', testUser);
    console.log('✅ Registration successful:', response.data.message);
    return true;
  } catch (error) {
    console.error('❌ Registration failed:', error.response?.data?.message || error.message);
    return false;
  }
}

async function testLogin() {
  console.log('🧪 Testing user login...');
  try {
    const response = await authRequest('POST', '/api/v3/auth/login', {
      email: testUser.email,
      password: testUser.password
    });
    
    if (response.data.session?.access_token) {
      authToken = response.data.session.access_token;
      console.log('✅ Login successful');
      return true;
    } else {
      console.error('❌ No access token received');
      return false;
    }
  } catch (error) {
    console.error('❌ Login failed:', error.response?.data?.message || error.message);
    return false;
  }
}

async function testCreateContact() {
  console.log('🧪 Testing contact creation...');
  try {
    const response = await authRequest('POST', '/api/v3/contacts', testContact);
    console.log('✅ Contact created successfully');
    return response.data.data?.id;
  } catch (error) {
    console.error('❌ Contact creation failed:', error.response?.data?.message || error.message);
    return null;
  }
}

async function testGetContacts() {
  console.log('🧪 Testing get contacts...');
  try {
    const response = await authRequest('GET', '/api/v3/contacts');
    console.log(`✅ Retrieved ${response.data.data?.length || 0} contacts`);
    return response.data.data;
  } catch (error) {
    console.error('❌ Get contacts failed:', error.response?.data?.message || error.message);
    return null;
  }
}

async function testUpdateContact(contactId) {
  console.log('🧪 Testing contact update...');
  try {
    const updateData = { fullName: 'Updated Test Contact' };
    const response = await authRequest('PATCH', `/api/v3/contacts/${contactId}`, updateData);
    console.log('✅ Contact updated successfully');
    return true;
  } catch (error) {
    console.error('❌ Contact update failed:', error.response?.data?.message || error.message);
    return false;
  }
}

async function testDeleteContact(contactId) {
  console.log('🧪 Testing contact deletion...');
  try {
    await authRequest('DELETE', `/api/v3/contacts/${contactId}`);
    console.log('✅ Contact deleted successfully');
    return true;
  } catch (error) {
    console.error('❌ Contact deletion failed:', error.response?.data?.message || error.message);
    return false;
  }
}

async function testLogout() {
  console.log('🧪 Testing logout...');
  try {
    await authRequest('POST', '/api/v3/auth/logout');
    console.log('✅ Logout successful');
    authToken = '';
    return true;
  } catch (error) {
    console.error('❌ Logout failed:', error.response?.data?.message || error.message);
    return false;
  }
}

// Main test runner
async function runTests() {
  console.log('🚀 Starting v3 API tests...\n');
  
  let contactId = null;
  
  // Test authentication flow
  const registrationSuccess = await testRegistration();
  if (!registrationSuccess) {
    console.log('⚠️  Registration failed, trying to login with existing user...');
  }
  
  const loginSuccess = await testLogin();
  if (!loginSuccess) {
    console.log('❌ Cannot proceed without authentication');
    return;
  }
  
  // Test contact management
  contactId = await testCreateContact();
  if (contactId) {
    await testGetContacts();
    await testUpdateContact(contactId);
    await testDeleteContact(contactId);
  }
  
  // Test logout
  await testLogout();
  
  console.log('\n🎉 All tests completed!');
}

// Error handling
process.on('unhandledRejection', (error) => {
  console.error('❌ Unhandled error:', error.message);
  process.exit(1);
});

// Run tests
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = {
  runTests,
  testRegistration,
  testLogin,
  testCreateContact,
  testGetContacts,
  testUpdateContact,
  testDeleteContact,
  testLogout
};
