#!/usr/bin/env node

/**
 * Setup script for v3 API with Supabase
 * Run with: node scripts/setup-v3.js
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

async function setupEnvironment() {
  console.log('🚀 Welcome to Address Book v3 API Setup!\n');
  console.log('This script will help you configure your environment variables for Supabase.\n');

  // Check if .env.local already exists
  const envPath = path.join(process.cwd(), '.env.local');
  if (fs.existsSync(envPath)) {
    const overwrite = await question('⚠️  .env.local already exists. Do you want to overwrite it? (y/N): ');
    if (overwrite.toLowerCase() !== 'y' && overwrite.toLowerCase() !== 'yes') {
      console.log('Setup cancelled. You can manually edit your .env.local file.');
      rl.close();
      return;
    }
  }

  console.log('\n📝 Please provide your Supabase project details:');
  console.log('You can find these in your Supabase dashboard under Settings → API\n');

  const supabaseUrl = await question('Enter your Supabase URL: ');
  const supabaseAnonKey = await question('Enter your Supabase Anon Key: ');
  const supabaseServiceKey = await question('Enter your Supabase Service Role Key (optional): ');

  // Validate required fields
  if (!supabaseUrl || !supabaseAnonKey) {
    console.log('\n❌ Error: Supabase URL and Anon Key are required!');
    rl.close();
    return;
  }

  // Create .env.local content
  const envContent = `# v3 API - Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=${supabaseUrl}
NEXT_PUBLIC_SUPABASE_ANON_KEY=${supabaseAnonKey}
${supabaseServiceKey ? `SUPABASE_SERVICE_ROLE_KEY=${supabaseServiceKey}` : '# SUPABASE_SERVICE_ROLE_KEY=your-service-role-key'}

# v2 API Configuration (Keep for backward compatibility)
# DATABASE_URL=postgresql://your-postgresql-url
# MONGODB_URI=your-mongodb-connection-string
# CLOUDINARY_NAME=your-cloudinary-name
# CLOUDINARY_API_KEY=your-cloudinary-api-key
# CLOUDINARY_API_SECRET=your-cloudinary-secret
# SALT_ROUNDS=10
# VERIFY_SECRET=random-string-with-special-characters

# Email Configuration (for v2 API)
# MAIL_CLIENT_ID=google-client-id
# MAIL_CLIENT_SECRET=google-client-secret
# MAIL_REFRESH_TOKEN=gmail-refresh-token
# MAIL_REDIRECT_URI=https://developers.google.com/oauthplayground
`;

  try {
    fs.writeFileSync(envPath, envContent);
    console.log('\n✅ Successfully created .env.local file!');
    
    console.log('\n📋 Next steps:');
    console.log('1. Set up your Supabase database schema');
    console.log('2. Run: npx prisma migrate dev');
    console.log('3. Start your development server: npm run dev');
    console.log('4. Visit: http://localhost:3000/v3');
    
    console.log('\n📚 For detailed instructions, check:');
    console.log('- SETUP_GUIDE.md');
    console.log('- V3_API_README.md');
    
  } catch (error) {
    console.log('\n❌ Error creating .env.local file:', error.message);
  }

  rl.close();
}

async function checkSupabaseProject() {
  console.log('\n🔍 Checking Supabase project setup...');
  
  const hasUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const hasKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
  
  if (hasUrl && hasKey) {
    console.log('✅ Supabase environment variables are configured!');
    
    // Test connection (basic check)
    try {
      const { createClient } = require('@supabase/supabase-js');
      const supabase = createClient(hasUrl, hasKey);
      
      // Try to get session (this will work even without auth)
      const { data, error } = await supabase.auth.getSession();
      console.log('✅ Supabase connection test successful!');
      
    } catch (error) {
      console.log('⚠️  Supabase connection test failed:', error.message);
      console.log('Please check your environment variables.');
    }
  } else {
    console.log('❌ Supabase environment variables not found.');
    console.log('Please run the setup to configure them.');
  }
}

// Main execution
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--check')) {
    await checkSupabaseProject();
  } else {
    await setupEnvironment();
  }
}

// Handle errors
process.on('unhandledRejection', (error) => {
  console.error('\n❌ Unhandled error:', error.message);
  process.exit(1);
});

process.on('SIGINT', () => {
  console.log('\n\n👋 Setup cancelled by user.');
  rl.close();
  process.exit(0);
});

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { setupEnvironment, checkSupabaseProject };
