# Copy this file to .env.local and fill in your actual values

# v3 API - Supabase Configuration (Required for v3 API)
NEXT_PUBLIC_SUPABASE_URL=your-supabase-project-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key

# v2 API Configuration (Keep for backward compatibility)
DATABASE_URL=postgresql://your-postgresql-url
MONGODB_URI=your-mongodb-connection-string
CLOUDINARY_NAME=your-cloudinary-name
CLOUDINARY_API_KEY=your-cloudinary-api-key
CLOUDINARY_API_SECRET=your-cloudinary-secret
SALT_ROUNDS=10
VERIFY_SECRET=random-string-with-special-characters

# Email Configuration (for v2 API)
MAIL_CLIENT_ID=google-client-id
MAIL_CLIENT_SECRET=google-client-secret
MAIL_REFRESH_TOKEN=gmail-refresh-token
MAIL_REDIRECT_URI=https://developers.google.com/oauthplayground

# v1 API Configuration (Deprecated)
CLIENT_ID=datastax-database-client-id
CLIENT_SECRET=datastax-database-client-secret
CLIENT_TOKEN=datastax-database-token
BASE_URL=datastax-server-url
