import type { NextApiRequest, NextApiResponse } from 'next'
import { supabase } from '@/config/supabase.config'

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Method not allowed' })
  }

  try {
    const { error } = await supabase.auth.signOut()

    if (error) {
      return res.status(400).json({ 
        success: false, 
        message: error.message 
      })
    }

    res.status(200).json({
      success: true,
      message: 'Logged out successfully'
    })
  } catch (error: any) {
    console.error('Logout error:', error)
    res.status(500).json({ 
      success: false, 
      message: 'Something went wrong during logout' 
    })
  }
}
