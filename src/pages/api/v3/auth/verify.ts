import type { NextApiRequest, NextApiResponse } from 'next'
import { supabase } from '@/config/supabase.config'

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Method not allowed' })
  }

  try {
    const { email, token, type = 'signup' } = req.body

    if (!email || !token) {
      return res.status(400).json({ 
        success: false, 
        message: 'Email and verification token are required' 
      })
    }

    // Verify the email with the token
    const { data, error } = await supabase.auth.verifyOtp({
      email,
      token,
      type: type as any
    })

    if (error) {
      return res.status(400).json({ 
        success: false, 
        message: error.message || 'Invalid verification token' 
      })
    }

    res.status(200).json({
      success: true,
      message: 'Email verified successfully! Please login',
      user: data.user
    })
  } catch (error: any) {
    console.error('Verification error:', error)
    res.status(500).json({ 
      success: false, 
      message: 'Something went wrong during verification' 
    })
  }
}
