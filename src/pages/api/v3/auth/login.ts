import type { NextApiRequest, NextApiResponse } from 'next'
import { supabase } from '@/config/supabase.config'

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Method not allowed' })
  }

  try {
    const { email, password } = req.body

    if (!email) {
      return res.status(400).json({ 
        success: false, 
        message: 'Email is required' 
      })
    }

    // If no password provided, just check if user exists
    if (!password) {
      const { data: userData, error: userError } = await supabase.auth.getUser()
      
      if (userError) {
        // Try to get user by email (this is a simplified check)
        return res.status(200).json({
          success: true,
          message: 'Account found, enter your password'
        })
      }
      
      return res.status(200).json({
        success: true,
        message: 'Account found, enter your password'
      })
    }

    // Sign in with email and password
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email,
      password
    })

    if (authError) {
      if (authError.message.includes('Invalid login credentials')) {
        return res.status(401).json({ 
          success: false, 
          message: 'Email/password not correct' 
        })
      }
      
      if (authError.message.includes('Email not confirmed')) {
        return res.status(401).json({ 
          success: false, 
          message: 'Account not verified!' 
        })
      }

      return res.status(400).json({ 
        success: false, 
        message: authError.message 
      })
    }

    if (!authData.user) {
      return res.status(404).json({
        success: false,
        message: 'Account not found, please register'
      })
    }

    res.status(200).json({
      success: true,
      message: 'Login successful',
      user: {
        id: authData.user.id,
        email: authData.user.email,
        full_name: authData.user.user_metadata?.full_name,
        user_type: authData.user.user_metadata?.user_type || 'user',
        verified: authData.user.email_confirmed_at ? true : false
      },
      session: authData.session
    })
  } catch (error: any) {
    console.error('Login error:', error)
    res.status(500).json({ 
      success: false, 
      message: 'Something went wrong during login' 
    })
  }
}
