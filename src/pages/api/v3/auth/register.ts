import type { NextApiRequest, NextApiResponse } from 'next'
import { supabase } from '@/config/supabase.config'

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Method not allowed' })
  }

  try {
    const { email, password, fullName } = req.body

    if (!email || !password || !fullName) {
      return res.status(400).json({ 
        success: false, 
        message: 'Email, password, and full name are required' 
      })
    }

    // Register user with Supabase Auth
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          full_name: fullName,
          user_type: 'user'
        }
      }
    })

    if (authError) {
      return res.status(400).json({ 
        success: false, 
        message: authError.message 
      })
    }

    res.status(201).json({
      success: true,
      message: 'Account created successfully. Please check your email for verification.',
      user: {
        id: authData.user?.id,
        email: authData.user?.email,
        full_name: fullName,
        user_type: 'user',
        verified: authData.user?.email_confirmed_at ? true : false
      }
    })
  } catch (error: any) {
    console.error('Registration error:', error)
    res.status(500).json({ 
      success: false, 
      message: 'Something went wrong during registration' 
    })
  }
}
