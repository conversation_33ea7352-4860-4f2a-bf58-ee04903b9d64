'use client'

import { AuthProvider } from '@/contexts/AuthContext'
import ContactListV3 from '@/components/ContactListV3'
import EnvironmentSetup from '@/components/EnvironmentSetup'

export default function V3ContactsPage() {
  // Check if Supabase environment variables are configured
  const isSupabaseConfigured =
    process.env.NEXT_PUBLIC_SUPABASE_URL &&
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

  if (!isSupabaseConfigured) {
    return <EnvironmentSetup />
  }

  return (
    <AuthProvider>
      <ContactListV3 />
    </AuthProvider>
  )
}
