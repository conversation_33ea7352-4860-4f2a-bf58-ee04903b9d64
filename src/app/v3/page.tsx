'use client'

import { AuthProvider } from '@/contexts/AuthContext'
import LandingV3 from '@/components/LandingV3'
import EnvironmentSetup from '@/components/EnvironmentSetup'

export default function V3HomePage() {
  // Check if Supabase environment variables are configured
  const isSupabaseConfigured =
    process.env.NEXT_PUBLIC_SUPABASE_URL &&
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

  if (!isSupabaseConfigured) {
    return <EnvironmentSetup />
  }

  return (
    <AuthProvider>
      <LandingV3 />
    </AuthProvider>
  )
}
