import { Fragment, useRef, useState } from 'react'
import { Dialog, Transition } from '@headlessui/react'

interface FormModalProps {
  open: boolean;
  setOpen(value: boolean): void;
  title: string;
  children: React.ReactNode;
  primaryBtn: string;
  onClickPrimaryBtn: () => void;
  loading: boolean;
  disabled: boolean;
}

export default function FormModal({
  open, setOpen, title, children, primaryBtn, onClickPrimaryBtn, loading, disabled
}: FormModalProps): JSX.Element {
  return (
    <Transition.Root show={open} as={Fragment}>
      <Dialog as="div" className="relative z-10" onClose={setOpen}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enterTo="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 sm:scale-100"
              leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
                <div>
                  <div className="mt-3 text-center sm:mt-5">
                    <Dialog.Title as="h2" className="text-xl font-semibold leading-6 text-gray-900">
                      {title}
                    </Dialog.Title>
                    <div className="mt-2">
                      {children}
                    </div>
                  </div>
                </div>
                <div className="mt-5 flex justify-center gap-7">
                  <button
                    disabled={loading || disabled}
                    type="button"
                    className={`inline-flex w-36 justify-center rounded-md bg-indigo-600 px-3 py-3 text-base font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 sm:col-start-2 ${loading || disabled ? "cursor-not-allowed" : ""}`}
                    onClick={onClickPrimaryBtn}
                  >
                    {primaryBtn}
                  </button>
                  <button
                    type="button"
                    className={`mt-3 inline-flex w-36 justify-center rounded-md bg-gray-300 px-3 py-3 text-base font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-300 sm:col-start-1 sm:mt-0 ${loading ? "cursor-not-allowed" : ""}`}
                    onClick={() => setOpen(false)}
                    disabled={loading}
                  >
                    Close
                  </button>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  )
}
