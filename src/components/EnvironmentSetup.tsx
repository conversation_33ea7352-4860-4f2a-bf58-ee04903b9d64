'use client'

import React from 'react'
import { Container } from './Container'
import { BackgroundImage } from './BackgroundImage'

export default function EnvironmentSetup() {
  const requiredEnvVars = [
    {
      name: 'NEXT_PUBLIC_SUPABASE_URL',
      description: 'Your Supabase project URL',
      example: 'https://your-project.supabase.co'
    },
    {
      name: 'NEXT_PUBLIC_SUPABASE_ANON_KEY',
      description: 'Your Supabase anonymous key',
      example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
    },
    {
      name: 'SUPABASE_SERVICE_ROLE_KEY',
      description: 'Your Supabase service role key (optional for client-side)',
      example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
    }
  ]

  return (
    <div className="relative py-20 sm:pb-24 sm:pt-36">
      <BackgroundImage className="-bottom-14 -top-36" />
      <Container className="relative">
        <div className="mx-auto max-w-4xl">
          <div className="text-center mb-12">
            <h1 className="font-display text-4xl font-bold tracking-tighter text-red-600 sm:text-6xl">
              Environment Setup Required
            </h1>
            <p className="mt-6 text-lg text-gray-600">
              To use the v3 API with Supabase, you need to configure your environment variables.
            </p>
          </div>

          <div className="bg-white rounded-lg shadow-lg p-8 mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Quick Setup Steps</h2>
            
            <div className="space-y-6">
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold">
                  1
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">Create a Supabase Project</h3>
                  <p className="text-gray-600 mt-1">
                    Go to <a href="https://supabase.com" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">supabase.com</a> and create a free account, then create a new project.
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold">
                  2
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">Get Your API Keys</h3>
                  <p className="text-gray-600 mt-1">
                    In your Supabase dashboard, go to Settings → API to find your project URL and API keys.
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold">
                  3
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">Create Environment File</h3>
                  <p className="text-gray-600 mt-1">
                    Create a <code className="bg-gray-100 px-2 py-1 rounded">.env.local</code> file in your project root with the following variables:
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-gray-900 rounded-lg p-6 mb-8">
            <h3 className="text-lg font-semibold text-white mb-4">Environment Variables Template</h3>
            <div className="bg-gray-800 rounded p-4 overflow-x-auto">
              <pre className="text-green-400 text-sm">
{`# Add these to your .env.local file
NEXT_PUBLIC_SUPABASE_URL=your-supabase-project-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key`}
              </pre>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-lg p-8">
            <h3 className="text-xl font-bold text-gray-900 mb-4">Required Environment Variables</h3>
            <div className="space-y-4">
              {requiredEnvVars.map((envVar, index) => (
                <div key={index} className="border-l-4 border-blue-500 pl-4">
                  <h4 className="font-semibold text-gray-900">{envVar.name}</h4>
                  <p className="text-gray-600 text-sm">{envVar.description}</p>
                  <p className="text-gray-500 text-xs mt-1">Example: {envVar.example}</p>
                </div>
              ))}
            </div>
          </div>

          <div className="mt-8 text-center">
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-yellow-800">
                    Important Note
                  </h3>
                  <div className="mt-2 text-sm text-yellow-700">
                    <p>
                      After creating your <code className="bg-yellow-100 px-1 rounded">.env.local</code> file, 
                      restart your development server with <code className="bg-yellow-100 px-1 rounded">npm run dev</code> 
                      for the changes to take effect.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="mt-8 text-center">
            <p className="text-gray-600">
              Need help? Check out the{' '}
              <a href="/SETUP_GUIDE.md" className="text-blue-600 hover:underline">
                detailed setup guide
              </a>{' '}
              or the{' '}
              <a href="https://supabase.com/docs" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                Supabase documentation
              </a>.
            </p>
          </div>
        </div>
      </Container>
    </div>
  )
}
