"use client";

import React, { useState } from 'react';
import { BackgroundImage } from './BackgroundImage'
import { Button } from './Button'
import { Container } from './Container'
import InputField from './InputField'
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import Notification from './modals/NotificationModal';

export interface NotificationProps {
  status: boolean;
  message: string;
}

export default function LandingV3(): JSX.Element {
  const [openModal, setOpenModal] = useState<boolean>(false);
  const [openVerified, setOpenVerified] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [notification, setNotification] = useState<NotificationProps>({ status: false, message: "" });
  const [isLogin, setIsLogin] = useState<boolean>(true);

  const { signUp, signIn, verifyOtp } = useAuth();
  const router = useRouter();

  const {
    values,
    handleChange,
    handleSubmit,
    isSubmitting,
    errors,
    resetForm,
    setFieldValue
  } = useFormik({
    initialValues: {
      email: "",
      password: "",
      fullName: "",
      code: ""
    },
    validationSchema: Yup.object({
      email: Yup.string().email('Invalid email address').required('Email is required'),
      password: isLogin ? Yup.string() : Yup.string().min(6, 'Password must be at least 6 characters').required('Password is required'),
      fullName: isLogin ? Yup.string() : Yup.string().min(2, 'Full name must be at least 2 characters').required('Full name is required'),
      code: Yup.string()
    }),
    onSubmit: async (values) => {
      try {
        setLoading(true);

        if (isLogin) {
          // Login flow
          if (!values.password) {
            // Just check if user exists
            setNotification({ status: true, message: "Account found, enter your password" });
            setOpenModal(true);
            return;
          }

          const result = await signIn(values.email, values.password);
          
          if (result.user) {
            setNotification({ status: true, message: "Login successful!" });
            setOpenModal(true);
            localStorage.setItem("email", values.email);
            router.push("/contact-lists");
            resetForm();
          }
        } else {
          // Registration flow
          const result = await signUp(values.email, values.password, values.fullName);
          
          if (result.user) {
            setNotification({ 
              status: true, 
              message: "Account created successfully! Please check your email for verification." 
            });
            setOpenModal(true);
            setOpenVerified(true);
            localStorage.setItem("email", values.email);
          }
        }
      } catch (error: any) {
        console.error('Auth error:', error);
        let errorMessage = "Something went wrong!";
        
        if (error.message) {
          if (error.message.includes('Invalid login credentials')) {
            errorMessage = "Email/password not correct";
          } else if (error.message.includes('Email not confirmed')) {
            errorMessage = "Account not verified!";
          } else if (error.message.includes('User already registered')) {
            errorMessage = "Email already exists";
          } else {
            errorMessage = error.message;
          }
        }
        
        setNotification({ status: false, message: errorMessage });
        setOpenModal(true);
      } finally {
        setLoading(false);
      }
    },
  });

  const accountVerification = async () => {
    try {
      setLoading(true);
      
      const result = await verifyOtp(values.email, values.code);
      
      if (result.user) {
        setNotification({ status: true, message: "Verified successfully! Please login" });
        setOpenModal(true);
        setOpenVerified(false);
        setIsLogin(true);
        resetForm();
      }
    } catch (error: any) {
      console.error('Verification error:', error);
      setNotification({ 
        status: false, 
        message: error.message || "Invalid verification code" 
      });
      setOpenModal(true);
    } finally {
      setLoading(false);
    }
  }

  const toggleMode = () => {
    setIsLogin(!isLogin);
    resetForm();
    setOpenVerified(false);
  }

  return (
    <div className="relative py-20 sm:pb-24 sm:pt-36">
      <BackgroundImage className="-bottom-14 -top-36" />
      <Container className="relative">
        <div className="mx-auto max-w-2xl lg:max-w-4xl lg:px-12">
          <h1 className="font-display text-5xl font-bold tracking-tighter text-blue-600 sm:text-7xl">
            <span className="sr-only">Address Book - </span>
            Your Personal Contact Manager
          </h1>
          <div className="mt-6 space-y-6 font-display text-2xl tracking-tight text-blue-900">
            <p>
              Keep all your important contacts organized and easily accessible.
              Store contact information, photos, and manage your personal network efficiently.
            </p>
          </div>
          
          <div className="mt-10 flex flex-col items-center gap-y-6">
            <div className="w-full max-w-md">
              <div className="bg-white rounded-lg shadow-lg p-6">
                <div className="flex justify-center mb-6">
                  <div className="flex bg-gray-100 rounded-lg p-1">
                    <button
                      type="button"
                      onClick={() => setIsLogin(true)}
                      className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                        isLogin 
                          ? 'bg-blue-600 text-white shadow-sm' 
                          : 'text-gray-500 hover:text-gray-700'
                      }`}
                    >
                      Login
                    </button>
                    <button
                      type="button"
                      onClick={() => setIsLogin(false)}
                      className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                        !isLogin 
                          ? 'bg-blue-600 text-white shadow-sm' 
                          : 'text-gray-500 hover:text-gray-700'
                      }`}
                    >
                      Register
                    </button>
                  </div>
                </div>

                <form onSubmit={handleSubmit} className="space-y-4">
                  {!isLogin && (
                    <InputField
                      name="fullName"
                      type="text"
                      value={values.fullName}
                      error={!!errors.fullName}
                      errorMessage={errors.fullName as string}
                      label="Full Name*"
                      placeholder="Enter your full name"
                      onChange={handleChange}
                    />
                  )}
                  
                  <InputField
                    name="email"
                    type="email"
                    value={values.email}
                    error={!!errors.email}
                    errorMessage={errors.email as string}
                    label="Email Address*"
                    placeholder="Enter your email"
                    onChange={handleChange}
                  />
                  
                  <InputField
                    name="password"
                    type="password"
                    value={values.password}
                    error={!!errors.password}
                    errorMessage={errors.password as string}
                    label={isLogin ? "Password" : "Password*"}
                    placeholder={isLogin ? "Enter password (optional for account check)" : "Enter your password"}
                    onChange={handleChange}
                  />
                  
                  <Button
                    type="submit"
                    className={`w-full ${loading || isSubmitting ? 'cursor-not-allowed bg-gray-400 hover:bg-gray-400' : ''}`}
                    disabled={loading || isSubmitting}
                  >
                    {loading || isSubmitting 
                      ? (isLogin ? "Signing in..." : "Creating account...") 
                      : (isLogin ? "Sign In" : "Create Account")
                    }
                  </Button>
                </form>
              </div>
            </div>
          </div>
        </div>
      </Container>

      {/* Verification Modal */}
      {openVerified && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3 text-center">
              <h3 className="text-lg font-medium text-gray-900">Verify Your Email</h3>
              <div className="mt-2 px-7 py-3">
                <p className="text-sm text-gray-500">
                  We've sent a verification code to your email. Please enter it below.
                </p>
                <div className="mt-4">
                  <InputField
                    name="code"
                    type="text"
                    value={values.code}
                    label="Verification Code"
                    placeholder="Enter verification code"
                    onChange={handleChange}
                  />
                </div>
              </div>
              <div className="items-center px-4 py-3">
                <Button
                  onClick={accountVerification}
                  className={`w-full ${loading ? 'cursor-not-allowed bg-gray-400 hover:bg-gray-400' : ''}`}
                  disabled={loading}
                >
                  {loading ? "Verifying..." : "Verify Account"}
                </Button>
                <button
                  onClick={() => setOpenVerified(false)}
                  className="mt-2 w-full px-4 py-2 bg-gray-300 text-gray-800 text-base font-medium rounded-md shadow-sm hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-300"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      <Notification
        show={openModal}
        setShow={setOpenModal}
        status={notification.status}
        message={notification.message}
      />
    </div>
  )
}
