CLIENT_ID='datastax-database-client-id' #Only if you want to use v1 API
CLIENT_SECRET='datastax-database-client-secret' #Only if you want to use v1 API
CLIENT_TOKEN='datastax-database-token' #Only if you want to use v1 API
BASE_URL='datastax-sserver-url' #Only if you want to use v1 API

# v2 API
CLOUDINARY_NAME='your-cloudinary-name' # for v2 API
CLOUDINARY_API_KEY='your-cloudinary-api-key' # for v2 API
CLOUDINARY_API_SECRET='your-cloudinary-secret' # for v2 API
MONGODB_URI='your-mongo-db-connection-string' # for v2 API
DATABASE_URL='postgresql:{your-postgresql-databse-url}' # for v2 API
SALT_ROUNDS=10 # for v2 API
VERIFY_SECRET='random-string-with-special-characters' # for v2 API
# env for sending mail
MAIL_CLIENT_ID='google-cliend-id' # for v2 API
MAIL_CLIENT_SECRET='google-cliend-secret' # for v2 API
MAIL_REFRESH_TOKEN='gmail-refresh-token' # for v2 API
MAIL_REDIRECT_URI=https://developers.google.com/oauthplayground # for v2 API
