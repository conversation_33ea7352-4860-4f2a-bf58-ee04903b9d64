{"name": "address-book", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "postinstall": "prisma generate"}, "dependencies": {"@heroicons/react": "^2.0.18", "@prisma/client": "^5.6.0", "@types/nodemailer": "^6.4.14", "axios": "^1.6.2", "bcrypt": "^5.1.1", "cassandra-driver": "^4.7.2", "cloudinary": "^1.41.0", "clsx": "^1.2.1", "formik": "^2.4.5", "googleapis": "^144.0.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.1", "next": "14.0.3", "nodemailer": "^6.9.16", "prisma": "^5.6.0", "react": "^18", "react-dom": "^18", "react-spinners": "^0.13.8", "uuid": "^9.0.1", "yup": "^1.3.2"}, "devDependencies": {"@headlessui/react": "^1.7.15", "@headlessui/tailwindcss": "^0.2.0", "@types/bcrypt": "^5.0.2", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/uuid": "^9.0.7", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.0.3", "postcss": "^8", "prettier": "^3.0.1", "prettier-plugin-tailwindcss": "^0.5.2", "sharp": "^0.32.0", "tailwindcss": "^3.3.0", "typescript": "^5"}}